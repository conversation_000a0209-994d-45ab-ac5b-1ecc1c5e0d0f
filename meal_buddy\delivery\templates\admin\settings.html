<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Settings - Meal Buddy</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background-color: #f0f2f5;
            min-height: 100vh;
        }

        .dashboard {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 20px;
            position: fixed;
            height: 100vh;
        }

        .logo {
            font-size: 24px;
            font-weight: 600;
            padding: 20px 0;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 20px;
        }

        .logo i {
            color: #3498db;
            margin-right: 10px;
        }

        .menu-item {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            align-items: center;
            text-decoration: none;
            color: white;
        }

        .menu-item:hover, .menu-item.active {
            background: #34495e;
        }

        .menu-item.active {
            background: #3498db;
        }

        .menu-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        /* Main Content Styles */
        .main-content {
            margin-left: 250px;
            padding: 30px;
            width: calc(100% - 250px);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e1e1e1;
        }

        .page-title h1 {
            font-size: 24px;
            color: #2c3e50;
        }

        .page-title p {
            color: #7f8c8d;
            margin-top: 5px;
        }

        /* Settings Sections */
        .settings-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            padding: 25px;
            margin-bottom: 30px;
        }

        .settings-section h2 {
            color: #2c3e50;
            font-size: 18px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f2f5;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .form-control:focus {
            border-color: #3498db;
            outline: none;
        }

        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .form-check input {
            margin-right: 10px;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
            border: none;
        }

        .btn-primary:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <i class="fas fa-utensils"></i>
                Meal Buddy
            </div>
            <a href="{% url 'admin_home' %}" class="menu-item">
                <i class="fas fa-home"></i>
                Dashboard
            </a>
            <a href="{% url 'admin_restaurants' %}" class="menu-item">
                <i class="fas fa-store"></i>
                Restaurants
            </a>
            <a href="{% url 'admin_customers' %}" class="menu-item">
                <i class="fas fa-users"></i>
                Customers
            </a>
            <a href="{% url 'admin_orders' %}" class="menu-item">
                <i class="fas fa-shopping-cart"></i>
                Orders
            </a>
            <a href="{% url 'admin_settings' %}" class="menu-item active">
                <i class="fas fa-cog"></i>
                Settings
            </a>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <div class="page-title">
                    <h1>Settings</h1>
                    <p>Manage your application settings</p>
                </div>
            </div>
            
            <!-- Settings Content -->
            <div class="settings-content">
                <!-- Add your settings form here -->
                <h2>General Settings</h2>
                <form>
                    <!-- Add your settings fields here -->
                </form>
            </div>
        </div>
    </div>
</body>
</html>



