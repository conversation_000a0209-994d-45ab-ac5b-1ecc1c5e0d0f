<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Menu Item - Meal Buddy</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Arial', sans-serif;
            padding: 20px 0;
        }
        .form-container {
            background-color: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 700px;
            margin: 0 auto;
        }
        h2 {
            color: #ff7e5f;
            margin-bottom: 30px;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background-color: #ff7e5f;
            color: white;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #eb6a4c;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        .messages {
            margin-bottom: 20px;
        }
        .message {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .url-preview {
            margin-top: 10px;
            max-width: 100%;
            height: 200px;
            border: 1px dashed #ddd;
            border-radius: 5px;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }
        .url-preview img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        .preview-text {
            color: #666;
            font-size: 14px;
        }
        .invalid-url {
            border-color: #dc3545 !important;
        }
        .form-check {
            margin-top: 15px;
        }
        .form-check-input {
            width: auto;
            margin-right: 10px;
        }
        .form-check-label {
            display: inline;
        }
        .button-group {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }
        .veg-badge {
            display: inline-block;
            width: 20px;
            height: 20px;
            background-color: green;
            border-radius: 50%;
            margin-right: 5px;
        }
        .non-veg-badge {
            display: inline-block;
            width: 20px;
            height: 20px;
            background-color: red;
            border-radius: 50%;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="form-container">
            <h2>Add New Menu Item</h2>

            {% if messages %}
            <div class="messages">
                {% for message in messages %}
                <div class="message {% if message.tags %}{{ message.tags }}{% endif %}">
                    {{ message }}
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <form action="{% url 'addMeuItem' restaurant_id %}" method="POST">
                {% csrf_token %}
                <div class="form-group">
                    <label for="name">Item Name</label>
                    <input type="text" id="name" name="name" required>
                </div>

                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea id="description" name="description" required></textarea>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="price">Price (₹)</label>
                            <input type="number" id="price" name="price" step="0.01" min="0" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="category">Category</label>
                            <select id="category" name="category" required>
                                <option value="">Select a category</option>
                                <option value="starters">Starters</option>
                                <option value="main_course">Main Course</option>
                                <option value="desserts">Desserts</option>
                                <option value="beverages">Beverages</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="image_url">Item Image URL</label>
                    <input type="url" id="image_url" name="image_url" 
                           placeholder="https://example.com/image.jpg" 
                           pattern="https?://.+" 
                           title="Please enter a valid URL starting with http:// or https://"
                           required
                           onchange="previewImage(this.value)">
                    <div class="url-preview" id="imagePreview">
                        <span class="preview-text">Image preview will appear here</span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_veg" name="is_veg" checked>
                            <label class="form-check-label" for="is_veg">
                                <span class="veg-badge"></span> Vegetarian
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_available" name="is_available" checked>
                            <label class="form-check-label" for="is_available">
                                Available
                            </label>
                        </div>
                    </div>
                </div>

                <div class="button-group">
                    <a href="{% url 'show_restaurants' %}" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn">Add Menu Item</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function previewImage(url) {
            const preview = document.getElementById('imagePreview');
            const urlInput = document.getElementById('image_url');

            // Clear previous content
            preview.innerHTML = '';

            if (url) {
                const img = new Image();
                img.onload = function() {
                    preview.innerHTML = '';
                    preview.appendChild(img);
                    urlInput.classList.remove('invalid-url');
                };
                img.onerror = function() {
                    preview.innerHTML = '<span class="preview-text">Invalid image URL</span>';
                    urlInput.classList.add('invalid-url');
                };
                img.src = url;
            } else {
                preview.innerHTML = '<span class="preview-text">Image preview will appear here</span>';
                urlInput.classList.remove('invalid-url');
            }
        }
    </script>
</body>
</html>