# Generated by Django 5.2 on 2025-04-22 15:59

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('username', models.<PERSON>r<PERSON><PERSON>(max_length=100)),
                ('password', models.Char<PERSON>ield(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('phone', models.Char<PERSON>ield(max_length=13)),
                ('address', models.<PERSON><PERSON><PERSON><PERSON>(max_length=300)),
            ],
        ),
    ]
