<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meal Buddy - Home</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }
        body {
            background: linear-gradient(to right, #ffecd2, #fcb69f);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            animation: fadeIn 1s ease-in-out;
        }
        .container {
            background-color: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            animation: slideUp 1s ease-in-out;
        }
        h1 {
            font-size: 2.5rem;
            color: #ff7e5f;
            margin-bottom: 20px;
        }
        p {
            font-size: 1.2rem;
            color: #333;
            margin-bottom: 30px;
        }
        .btn {
            display: inline-block;
            padding: 10px 25px;
            margin: 0 10px;
            border-radius: 30px;
            background-color: #ff7e5f;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background-color: #eb6a4c;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to   { opacity: 1; }
        }

        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to   { transform: translateY(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to Meal Buddy 🍽️</h1>
        <p>Your personal companion for delicious meals and healthy eating.</p>
        <a href="{% url 'open_login' %}" class="btn">Login</a>
        <a href="{% url 'signup' %}" class="btn">Sign Up</a>
    </div>
</body>
</html>
