<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ restaurant.name }} - Menu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Arial', sans-serif;
        }

        .restaurant-header {
            background-color: #fff;
            padding: 2rem 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .restaurant-info {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .restaurant-image {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 10px;
        }

        .menu-category {
            background-color: #fff;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .category-title {
            color: #ff7e5f;
            border-bottom: 2px solid #ff7e5f;
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s;
        }

        .menu-item:hover {
            background-color: #f8f9fa;
        }

        .menu-item-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            margin-right: 1rem;
        }

        .menu-item-details {
            flex-grow: 1;
        }

        .menu-item-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .menu-item-description {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            max-width: calc(100% - 220px);
        }

        .menu-item-price {
            font-weight: bold;
            color: #28a745;
        }

        .add-to-cart-btn {
            background-color: #ff7e5f;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .add-to-cart-btn:hover {
            background-color: #e66f52;
            cursor: pointer;
            font-weight: bold;
        }

        .veg-icon {
            color: green;
            margin-right: 0.5rem;
        }

        .non-veg-icon {
            color: red;
            margin-right: 0.5rem;
        }

        .back-btn {
            margin-bottom: 1rem;
            color: #ff7e5f;
            text-decoration: none;
        }

        .back-btn:hover {
            color: #e66f52;
        }

        /* Review styles */
        .reviews-section {
            margin-top: 2rem;
        }

        .reviews-container {
            max-height: 400px;  /* Fixed height for scrollable area */
            overflow-y: auto;   /* Enable vertical scrolling */
            padding-right: 10px; /* Space for scrollbar */
        }

        /* Customize scrollbar */
        .reviews-container::-webkit-scrollbar {
            width: 8px;
        }

        .reviews-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .reviews-container::-webkit-scrollbar-thumb {
            background: #ff7e5f;
            border-radius: 4px;
        }

        .reviews-container::-webkit-scrollbar-thumb:hover {
            background: #e66f52;
        }

        .review-item {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
            background-color: #fff;  /* Ensure background is white */
        }

        .review-item:last-child {
            border-bottom: none;
        }

        .review-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .reviewer-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .review-date {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .review-rating {
            font-size: 1.1rem;
        }

        .review-comment {
            color: #333;
            margin: 0;
            line-height: 1.5;
        }

        .text-warning {
            color: #ffc107;
        }

        .category-nav {
            position: sticky;
            top: 0;
            background: #fff;
            padding: 1rem 0;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .nav-pills .nav-link {
            color: #ff7e5f;
            border-radius: 25px;
            margin: 0 0.5rem;
        }

        .nav-pills .nav-link.active {
            background-color: #ff7e5f;
            color: white;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            padding: 1rem 0;
        }

        .menu-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }

        .menu-card:hover {
            transform: translateY(-5px);
        }

        .menu-card-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }

        .menu-card-body {
            padding: 1rem;
        }

        .menu-card-title {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }

        .menu-card-description {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            height: 40px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .menu-card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 0.5rem;
            border-top: 1px solid #eee;
        }

        .menu-card-price {
            font-weight: bold;
            color: #28a745;
            font-size: 1.1rem;
        }

        .add-to-cart-btn {
            padding: 0.4rem 1rem;
            font-size: 0.9rem;
        }

        /* Adjust existing styles */
        .menu-category {
            margin-bottom: 1rem;
        }

        .category-title {
            margin-bottom: 1rem;
        }

        /* Make reviews section sticky */
        .reviews-section {
            position: sticky;
            bottom: 0;
            margin-top: 2rem;
            max-height: 400px;
            overflow-y: auto;
        }

        /* Floating cart button */
        .floating-cart {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background-color: #ff7e5f;
            color: white;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
            text-decoration: none;
            transition: transform 0.3s, background-color 0.3s;
            z-index: 1000;
        }
        
        .floating-cart:hover {
            transform: scale(1.1);
            background-color: #e66f52;
            color: white;
        }
        
        .cart-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #28a745;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        /* Toast notification */
        .toast-container {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 1050;
        }
        
        .toast {
            background-color: rgba(40, 167, 69, 0.9);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="{% url 'customer_home' %}" class="back-btn mt-3 d-inline-block">
            <i class="fas fa-arrow-left"></i> Back to Restaurants
        </a>

        <div class="restaurant-header">
            <div class="container">
                <div class="restaurant-info">
                    <img src="{{ restaurant.image_url }}" alt="{{ restaurant.name }}" class="restaurant-image">
                    <div>
                        <h1>{{ restaurant.name }}</h1>
                        <p class="text-muted mb-1">{{ restaurant.cuisine }}</p>
                        <p class="mb-1">
                            {% for i in "12345" %}
                                {% if forloop.counter <= restaurant.rating %}
                                    <span class="text-warning">★</span>
                                {% else %}
                                    <span class="text-muted">☆</span>
                                {% endif %}
                            {% endfor %}
                            <span class="text-muted">({{ restaurant.rating }})</span>
                        </p>
                        <p class="mb-1"><i class="fas fa-clock"></i> {{ restaurant.opening_hours }}</p>
                        <p class="mb-0"><i class="fas fa-phone"></i> {{ restaurant.phone }}</p>
                    </div>
                </div>
            </div>
        </div>

        {% if menu_items %}
            <!-- Category Navigation -->
            <div class="category-nav">
                <nav class="nav nav-pills nav-fill mb-4">
                    {% regroup menu_items by category as category_list %}
                    {% for category in category_list %}
                        <a class="nav-link {% if forloop.first %}active{% endif %}" 
                           href="#category-{{ category.grouper|slugify }}"
                           data-bs-toggle="pill">
                            {{ category.grouper }}
                        </a>
                    {% endfor %}
                </nav>
            </div>

            <!-- Menu Items -->
            <div class="tab-content">
                {% for category in category_list %}
                    <div class="tab-pane fade {% if forloop.first %}show active{% endif %}" 
                         id="category-{{ category.grouper|slugify }}">
                        <div class="menu-category">
                            <h2 class="category-title">{{ category.grouper }}</h2>
                            <div class="menu-grid">
                                {% for item in category.list %}
                                    <div class="menu-card">
                                        <img src="{{ item.image_url }}" alt="{{ item.name }}" class="menu-card-image">
                                        <div class="menu-card-body">
                                            <h5 class="menu-card-title">
                                                {% if item.is_veg %}
                                                    <i class="fas fa-leaf veg-icon"></i>
                                                {% else %}
                                                    <i class="fas fa-drumstick-bite non-veg-icon"></i>
                                                {% endif %}
                                                {{ item.name }}
                                            </h5>
                                            <p class="menu-card-description">{{ item.description }}</p>
                                            <div class="menu-card-footer">
                                                <span class="menu-card-price">₹{{ item.price }}</span>
                                                <form action="{% url 'add_to_cart' item.id %}" method="POST" class="d-inline">
                                                    {% csrf_token %}
                                                    <input type="hidden" name="quantity" value="1">
                                                    <button type="submit" class="add-to-cart-btn">
                                                        <i class="fas fa-plus"></i> Add
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="alert alert-info text-center">
                No menu items available for this restaurant.
            </div>
        {% endif %}

        <!-- Reviews Section -->
        <div class="reviews-section menu-category">
            <h2 class="category-title">Customer Reviews</h2>
            
            {% if reviews %}
                <div class="reviews-container">
                    {% for review in reviews %}
                        <div class="review-item">
                            <div class="review-header">
                                <div class="reviewer-info">
                                    <strong>{{ review.customer.username }}</strong>
                                    <span class="review-date">{{ review.created_at|date:"M d, Y" }}</span>
                                </div>
                                <div class="review-rating">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= review.rating %}
                                            <span class="text-warning">★</span>
                                        {% else %}
                                            <span class="text-muted">☆</span>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                            <p class="review-comment">{{ review.comment }}</p>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <p class="text-muted text-center">No reviews yet. Be the first to review!</p>
            {% endif %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Floating Cart Button -->
    {% if request.session.cart %}
        {% with cart_count=request.session.cart.items|length|add:"-1" %}
            {% if cart_count > 0 %}
                <a href="{% url 'view_cart' %}" class="floating-cart">
                    <i class="fas fa-shopping-cart fa-lg"></i>
                    <span class="cart-badge">{{ cart_count }}</span>
                </a>
            {% endif %}
        {% endwith %}
    {% endif %}

    <!-- Toast Notifications -->
    <div class="toast-container">
        {% if messages %}
            {% for message in messages %}
                <div class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000">
                    <div class="toast-body">
                        {{ message }}
                    </div>
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <script>
        // Initialize toasts
        document.addEventListener('DOMContentLoaded', function() {
            var toastElList = [].slice.call(document.querySelectorAll('.toast'));
            var toastList = toastElList.map(function(toastEl) {
                return new bootstrap.Toast(toastEl);
            });
            
            // Show all toasts
            toastList.forEach(toast => toast.show());
        });
    </script>
</body>
</html>





{% include 'includes/footer.html' %}




