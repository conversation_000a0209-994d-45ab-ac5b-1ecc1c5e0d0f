# Generated by Django 5.2 on 2025-04-30 13:42

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('delivery', '0002_restaurant'),
    ]

    operations = [
        migrations.CreateModel(
            name='MenuItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('price', models.DecimalField(decimal_places=2, max_digits=8)),
                ('category', models.CharField(choices=[('starters', 'Starters'), ('main_course', 'Main Course'), ('desserts', 'Desserts'), ('beverages', 'Beverages')], max_length=20)),
                ('image_url', models.URLField()),
                ('is_veg', models.BooleanField(default=True)),
                ('is_available', models.BooleanField(default=True)),
                ('restaurant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='menu_items', to='delivery.restaurant')),
            ],
            options={
                'ordering': ['category', 'name'],
            },
        ),
    ]
