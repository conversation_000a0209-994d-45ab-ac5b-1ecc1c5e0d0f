<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Meal Buddy</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background-color: #f0f2f5;
            min-height: 100vh;
        }

        .dashboard {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 20px;
            position: fixed;
            height: 100vh;
        }

        .logo {
            font-size: 24px;
            font-weight: 600;
            padding: 20px 0;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 20px;
        }

        .logo i {
            color: #3498db;
            margin-right: 10px;
        }

        .menu-item {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            align-items: center;
            text-decoration: none;
            color: white;
        }

        .menu-item:hover {
            background: #34495e;
        }

        .menu-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        /* Main Content Styles */
        .main-content {
            margin-left: 250px;
            padding: 30px;
            width: calc(100% - 250px);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e1e1e1;
        }

        .welcome-text h1 {
            font-size: 24px;
            color: #2c3e50;
        }

        .welcome-text p {
            color: #7f8c8d;
            margin-top: 5px;
        }

        .admin-profile {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .admin-profile img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* Dashboard Cards */
        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 20px;
        }

        .restaurants-icon {
            background: #e8f4ff;
            color: #3498db;
        }

        .orders-icon {
            background: #fff2e8;
            color: #ff9f43;
        }

        .users-icon {
            background: #e8fff1;
            color: #2ecc71;
        }

        .card h3 {
            color: #2c3e50;
            font-size: 18px;
        }

        .card p {
            color: #7f8c8d;
            font-size: 14px;
        }

        .quick-actions {
            margin-top: 30px;
        }

        .quick-actions h2 {
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .action-button {
            padding: 15px 20px;
            border-radius: 8px;
            text-decoration: none;
            color: white;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s ease;
        }

        .action-button:hover {
            transform: translateY(-3px);
        }

        .add-restaurant {
            background: #3498db;
        }

        .view-restaurants {
            background: #2ecc71;
        }

        .action-button i {
            margin-right: 10px;
        }

        /* Recent Orders Table */
        .recent-orders {
            margin-top: 40px;
        }

        .recent-orders h2 {
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .orders-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .orders-table th, 
        .orders-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #f0f2f5;
        }

        .orders-table th {
            background: #f8f9fa;
            color: #2c3e50;
            font-weight: 500;
        }

        .orders-table tr:last-child td {
            border-bottom: none;
        }

        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background-color: #ffefd5;
            color: #ff9f43;
        }

        .status-confirmed {
            background-color: #e3f2fd;
            color: #2196f3;
        }

        .status-preparing {
            background-color: #e8f5e9;
            color: #4caf50;
        }

        .status-out_for_delivery {
            background-color: #fff8e1;
            color: #ffc107;
        }

        .status-delivered {
            background-color: #e8f5e9;
            color: #4caf50;
        }

        .status-cancelled {
            background-color: #ffebee;
            color: #f44336;
        }

        .view-order-btn {
            padding: 6px 12px;
            background: #3498db;
            color: white;
            border-radius: 4px;
            text-decoration: none;
            font-size: 12px;
            transition: background 0.3s;
        }

        .view-order-btn:hover {
            background: #2980b9;
        }

        .card-value {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="dashboard">
         <!-- Include Admin Sidebar --> {% include 'includes/admin_sidebar.html' %}
        <!-- Sidebar -->
        <!-- <div class="sidebar">
            <div class="logo">
                <i class="fas fa-utensils"></i>
                Meal Buddy
            </div>
            <a href="{% url 'admin_home' %}" class="menu-item">
                <i class="fas fa-home"></i>
                Dashboard
            </a>
            <a href="{% url 'admin_restaurants' %}" class="menu-item">
                <i class="fas fa-store"></i>
                Restaurants
            </a>
            <a href="{% url 'admin_customers' %}" class="menu-item">
                <i class="fas fa-users"></i>
                Customers
            </a>
            <a href="{% url 'admin_orders' %}" class="menu-item">
                <i class="fas fa-shopping-cart"></i>
                Orders
            </a>
            <a href="{% url 'admin_settings' %}" class="menu-item">
                <i class="fas fa-cog"></i>
                Settings
            </a> -->


        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <div class="welcome-text">
                    <h1>Welcome Back, Admin!</h1>
                    <p>Here's what's happening with your restaurants today.</p>
                </div>
                <div class="admin-profile">
                    <span>{{username}}</span>
                    <img src="https://ui-avatars.com/api/{{username}}" alt="Admin Profile">
                    <!-- <a href="{% url 'logout' %}" class="btn btn-outline-danger ms-3" style="font-size: 0.9rem;">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a> -->
                </div>
            </div>

            <!-- Dashboard Cards -->
            <div class="dashboard-cards">
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon restaurants-icon">
                            <i class="fas fa-store"></i>
                        </div>
                        <div>
                            <h3>Total Restaurants</h3>
                            <p>Active partnerships</p>
                        </div>
                    </div>
                    <div class="card-value">{{ restaurant_count }}</div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-icon orders-icon">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <div>
                            <h3>Total Orders</h3>
                            <p>Last 24 hours</p>
                        </div>
                    </div>
                    <div class="card-value">{{ recent_orders_count }}</div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-icon users-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <h3>Active Users</h3>
                            <p>Registered customers</p>
                        </div>
                    </div>
                    <div class="card-value">{{ customer_count }}</div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h2>Quick Actions</h2>
                <div class="action-buttons">
                    <a href="{% url 'open_add_restaurant' %}" class="action-button add-restaurant">
                        <i class="fas fa-plus"></i>
                        Add New Restaurant
                    </a>
                    <a href="{% url 'admin_restaurants' %}" class="action-button view-restaurants">
                        <i class="fas fa-list"></i>
                        View All Restaurants
                    </a>
                </div>
            </div>

            <!-- Recent Customers -->
            <div class="recent-orders">
                <h2>Recent Customers</h2>
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th>Customer ID</th>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Address</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in recent_customers %}
                        <tr>
                            <td>#{{ customer.id }}</td>
                            <td>{{ customer.username }}</td>
                            <td>{{ customer.email }}</td>
                            <td>{{ customer.phone }}</td>
                            <td>{{ customer.address }}</td>
                            <td>
                                <a href="{% url 'admin_view_customer' customer.id %}" class="view-order-btn">View</a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" style="text-align: center;">No recent customers</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Recent Orders -->
            <div class="recent-orders">
                <h2>Recent Orders</h2>
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Restaurant</th>
                            <th>Amount</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in recent_orders %}
                        <tr>
                            <td>#{{ order.id }}</td>
                            <td>{{ order.customer.username }}</td>
                            <td>{{ order.restaurant.name }}</td>
                            <td>₹{{ order.total_amount }}</td>
                            <td>{{ order.created_at|date:"M d, Y H:i" }}</td>
                            <td>
                                <span class="status-badge status-{{ order.status }}">
                                    {{ order.get_status_display }}
                                </span>
                            </td>
                            <td>
                                <a href="{% url 'admin_view_order' order.id %}" class="view-order-btn">View</a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" style="text-align: center;">No recent orders</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
            </div>
</body>
</html>


