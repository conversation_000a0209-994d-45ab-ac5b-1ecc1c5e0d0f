<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Restaurants - Meal Buddy <PERSON></title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }

        .dashboard {
            display: flex;
        }

        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title h1 {
            margin: 0;
            font-size: 1.8rem;
            color: #333;
        }

        .page-title p {
            margin: 5px 0 0;
            color: #777;
        }

        .action-buttons {
            margin-bottom: 20px;
        }

        .action-button {
            display: inline-flex;
            align-items: center;
            padding: 10px 20px;
            background-color:  #0992ee;;
            color: white;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
        }

        .action-button i {
            margin-right: 8px;
        }

        .action-button:hover {
            background-color: #0ec26b;
            transform: translateY(-2px);
        }

        .restaurant-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .restaurant-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s;
        }

        .restaurant-card:hover {
            transform: translateY(-5px);
        }

        .restaurant-image {
            width: 100%;
            height: 180px;
            object-fit: cover;
        }

        .restaurant-info {
            padding: 15px;
        }

        .restaurant-name {
            font-size: 1.2rem;
            margin: 0 0 10px 0;
            color: #333;
        }

        .restaurant-details p {
            margin: 5px 0;
            color: #666;
            font-size: 0.9rem;
        }

        .rating {
            margin: 10px 0;
            color: #ffd700;
        }

        .card-actions {
            display: flex;
            margin-top: 15px;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }

        .card-action {
            flex: 1;
            text-align: center;
            padding: 8px;
            color: #555;
            text-decoration: none;
            font-size: 0.9rem;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .card-action:hover {
            background-color: #f5f5f5;
        }

        .edit-action:hover {
            color: #2196F3;
        }

        .delete-action:hover {
            color: #F44336;
        }

        .menu-action:hover {
            color: #4CAF50;
        }

        .no-restaurants {
            text-align: center;
            padding: 50px;
            background: white;
            border-radius: 10px;
            color: #777;
        }
    </style>
</head>

<body>
    <div class="dashboard">
        <!-- Include Admin Sidebar --> {% include 'includes/admin_sidebar.html' %}
        <!-- Main Content -->
        <div class="main-content"> <!-- Header -->
            <div class="header">
                <div class="page-title">
                    <h1>Manage Restaurants</h1>
                    <p>View and manage all restaurants in the system</p>
                </div>
            </div>
            <!-- Action Buttons -->
            <div class="action-buttons"> <a href="{% url 'open_add_restaurant' %}" class="action-button add-restaurant">
                    <i class="fas fa-plus"></i> Add New Restaurant
                </a> </div>
            <!-- Restaurant Grid -->
            {% if restaurants %}
            <div class="restaurant-grid">
                {% for restaurant in restaurants %}
                <div class="restaurant-card">
                    <img src="{{ restaurant.image_url }}" alt="{{ restaurant.name }}" class="restaurant-image">
                    <div class="restaurant-info">
                        <h2 class="restaurant-name">{{ restaurant.name }}</h2>
                        <div class="restaurant-details">
                            <p><strong>Cuisine:</strong> {{ restaurant.cuisine }}</p>
                            <p><strong>Phone:</strong> {{ restaurant.phone }}</p>
                            <p><strong>Hours:</strong> {{ restaurant.opening_hours }}</p>
                            <div class="rating">
                                {% for i in "12345" %}
                                {% if forloop.counter <= restaurant.rating %} <i class="fas fa-star"></i>
                                    {% else %}
                                    <i class="far fa-star"></i>
                                    {% endif %}
                                    {% endfor %}
                            </div>
                        </div>
                        <div class="card-actions">
                            <a href="{% url 'update_restaurant' restaurant.id %}" class="card-action edit-action">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <a href="{% url 'delete_restaurant' restaurant.id %}" class="card-action delete-action">
                                <i class="fas fa-trash"></i> Delete
                            </a>
                            <a href="{% url 'manage_menu' restaurant.id %}" class="card-action menu-action">
                                <i class="fas fa-utensils"></i> Menu
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="no-restaurants">
                <p>No restaurants available yet. Click "Add New Restaurant" to get started.</p>
            </div>
            {% endif %}
        </div>
    </div>
</body>

</html>