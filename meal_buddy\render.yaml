databases:
  - name: meal-mate-db
    databaseName: meal_mate
    user: meal_mate_user

services:
  - type: web
    name: meal-mate
    runtime: python
    buildCommand: "./build.sh"
    startCommand: "gunicorn meal_buddy.wsgi:application"
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: meal-mate-db
          property: connectionString
      - key: SECRET_KEY
        generateValue: true
      - key: WEB_CONCURRENCY
        value: 4
