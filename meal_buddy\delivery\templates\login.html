<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Page</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(to right, #ffecd2, #fcb69f);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
        }
        .form-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #ff7e5f;
            border: none;
            padding: 10px 20px;
            border-radius: 30px;
            color: white;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
            transition: background-color 0.3s ease;
        }
        button:hover {
            background: #eb6a4c;
        }
        .error-message {
            color: red;
            font-size: 0.8rem;
            margin-top: 0.2rem;
        }
        h2 {
            text-align: center;
            color: #333;
            margin-bottom: 1.5rem;
        }
        .forgot-password {
            text-align: right;
            margin: 0.5rem 0 1rem;
        }
        .forgot-password a {
            color: #666;
            text-decoration: none;
            font-size: 0.9rem;
        }
        .forgot-password a:hover {
            color: #ff7e5f;
        }
        .messages {
            margin-bottom: 20px;
        }
        .message {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h2>Welcome Back!</h2>
        
        {% if messages %}
        <div class="messages">
            {% for message in messages %}
            <div class="message {% if message.tags %}{{ message.tags }}{% endif %}">
                {{ message }}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <form action="{% url 'process_signin' %}" method="POST">
            {% csrf_token %}
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required autofocus>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            <div class="forgot-password">
                <a>Forgot Password?</a>
            </div>
            <button type="submit">Log In</button>
        </form>
        <p style="text-align: center; margin-top: 1rem;">
            Don't have an account? <a href="{% url 'signup' %}" style="color: #ff7e5f; text-decoration: none;">Sign Up</a>
        </p>
    </div>
</body>
</html>
