{% load custom_filters %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Cart - Meal Buddy</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 800;
            color: #ff7e5f;
            font-size: 1.5rem;
        }
        
        .cart-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            padding: 30px;
            margin-top: 30px;
            margin-bottom: 30px;
        }
        
        .cart-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        
        .cart-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .item-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            margin-right: 15px;
        }
        
        .item-details {
            flex-grow: 1;
        }
        
        .item-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .item-price {
            color: #666;
            font-size: 0.9rem;
        }
        
        .quantity-controls {
            display: flex;
            align-items: center;
            margin-right: 20px;
        }
        
        .quantity-btn {
            background-color: #f0f0f0;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 1rem;
        }
        
        .quantity-display {
            margin: 0 10px;
            font-weight: 600;
        }
        
        .item-total {
            font-weight: 600;
            min-width: 80px;
            text-align: right;
        }
        
        .cart-summary {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .summary-total {
            font-size: 1.2rem;
            font-weight: 700;
            color: #ff7e5f;
        }
        
        .btn-checkout {
            background-color: #ff7e5f;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 50px;
            font-weight: 600;
            margin-top: 20px;
            transition: background-color 0.3s;
        }
        
        .btn-checkout:hover {
            background-color: #e66f52;
        }
        
        .btn-continue {
            background-color: transparent;
            color: #666;
            border: 1px solid #ddd;
            padding: 12px 25px;
            border-radius: 50px;
            font-weight: 600;
            margin-top: 20px;
            transition: all 0.3s;
        }
        
        .btn-continue:hover {
            background-color: #f0f0f0;
            color: #333;
        }
        
        .empty-cart {
            text-align: center;
            padding: 50px 0;
        }
        
        .empty-cart i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        .empty-cart h3 {
            margin-bottom: 15px;
            color: #555;
        }
        
        .empty-cart p {
            color: #888;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    {% include 'includes/navbar.html' %}

    <div class="container">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show mt-3" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
        
        <div class="cart-container">
            <div class="cart-header">
                <h1>Your Cart</h1>
                {% if restaurant %}
                    <p>Ordering from: <strong>{{ restaurant.name }}</strong></p>
                {% endif %}
            </div>
            
            {% if items %}
                {% for item in items %}
                    <div class="cart-item">
                        <img src="{{ item.image_url }}" alt="{{ item.name }}" class="item-image">
                        <div class="item-details">
                            <div class="item-name">{{ item.name }}</div>
                            <div class="item-price">₹{{ item.price }} each</div>
                        </div>
                        <div class="quantity-controls">
                            <form action="{% url 'update_cart_item_with_action' item.id 'decrease' %}" method="POST" class="d-inline">
                                {% csrf_token %}
                                <button type="submit" class="quantity-btn">
                                    <i class="fas fa-minus"></i>
                                </button>
                            </form>
                            <span class="quantity-display">{{ item.quantity }}</span>
                            <form action="{% url 'update_cart_item_with_action' item.id 'increase' %}" method="POST" class="d-inline">
                                {% csrf_token %}
                                <button type="submit" class="quantity-btn">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </form>
                        </div>
                        <div class="item-total">₹{{ item.total |floatformat:2}}</div>
                        <form action="{% url 'update_cart_item_with_action' item.id 'remove' %}" method="POST" class="ms-3">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                {% endfor %}
                
                <div class="cart-summary">
                    <div class="summary-row">
                        <span>Subtotal:</span>
                        <span>₹{{ total }}</span>
                    </div>
                    <div class="summary-row">
                        <span>Delivery Fee:</span>
                        <span>₹{{ delivery_fee }}</span>
                    </div>
                    <div class="summary-row">
                        <span>Tax (5%):</span>
                        <span>₹{{ tax }}</span>
                    </div>
                    <div class="summary-row summary-total">
                        <span>Total:</span>
                        <span>₹{{ final_total }}</span>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between mt-4">
                    <a href="{% url 'clear_cart' %}" class="btn btn-outline-danger">
                        <i class="fas fa-trash me-2"></i> Clear Cart
                    </a>
                    <div>
                        <a href="{% url 'show_restaurants' %}" class="btn-continue me-2">
                            <i class="fas fa-utensils me-2"></i> Continue Shopping
                        </a>
                        <a href="{% url 'checkout' %}" class="btn-checkout">
                            <i class="fas fa-shopping-cart me-2"></i> Proceed to Checkout
                        </a>
                    </div>
                </div>
            {% else %}
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <h3>Your cart is empty</h3>
                    <p>Looks like you haven't added any items to your cart yet.</p>
                    <a href="{% url 'show_restaurants' %}" class="btn-checkout">
                        <i class="fas fa-utensils me-2"></i> Browse Restaurants
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>




