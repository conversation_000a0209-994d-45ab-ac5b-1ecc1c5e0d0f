<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Menu - {{ restaurant.name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Arial', sans-serif;
        }
        
        .header {
            background-color: #fff;
            padding: 2rem 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .restaurant-info {
            display: flex;
            align-items: center;
            gap: 2rem;
        }
        
        .restaurant-image {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 10px;
        }
        
        .menu-category {
            background-color: #fff;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .category-title {
            color: #ff7e5f;
            border-bottom: 2px solid #ff7e5f;
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s;
        }
        
        .menu-item:hover {
            background-color: #f8f9fa;
        }
        
        .menu-item-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            margin-right: 1rem;
        }
        
        .menu-item-details {
            flex-grow: 1;
        }
        
        .menu-item-title {
            font-weight: bold;
            margin-bottom: 0.25rem;
            display: flex;
            align-items: center;
        }
        
        .menu-item-description {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        
        .menu-item-price {
            font-weight: bold;
            color: #28a745;
        }
        
        .menu-item-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-action {
            padding: 0.4rem 0.8rem;
            border-radius: 5px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }
        
        .btn-edit {
            background-color: #ffc107;
            color: #212529;
        }
        
        .btn-delete {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-add {
            background-color: #ff7e5f;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        
        .btn-add:hover {
            background-color: #e66f52;
        }
        
        .veg-icon {
            color: green;
            margin-right: 0.5rem;
        }
        
        .non-veg-icon {
            color: red;
            margin-right: 0.5rem;
        }
        
        .availability-badge {
            font-size: 0.8rem;
            padding: 0.2rem 0.5rem;
            border-radius: 20px;
            margin-left: 0.5rem;
        }
        
        .available {
            background-color: #d4edda;
            color: #155724;
        }
        
        .unavailable {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .back-btn {
            margin-bottom: 1rem;
            color: #ff7e5f;
            text-decoration: none;
            display: inline-block;
        }
        
        .back-btn:hover {
            color: #e66f52;
        }
        
        .empty-category {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
        }
        
        .add-category-btn {
            margin-left: 1rem;
            font-size: 0.9rem;
        }
        
        .category-nav {
            position: sticky;
            top: 0;
            background: #fff;
            padding: 1rem 0;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .nav-pills .nav-link {
            color: #ff7e5f;
            border-radius: 25px;
            margin: 0 0.5rem;
        }
        
        .nav-pills .nav-link.active {
            background-color: #ff7e5f;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="{% url 'admin_restaurants' %}" class="back-btn mt-3">
            <i class="fas fa-arrow-left"></i> Back to Restaurants
        </a>
        
        <div class="header">
            <div class="container">
                <div class="restaurant-info">
                    <img src="{{ restaurant.image_url }}" alt="{{ restaurant.name }}" class="restaurant-image">
                    <div>
                        <h1>{{ restaurant.name }} - Menu Management</h1>
                        <p class="text-muted">{{ restaurant.cuisine }}</p>
                    </div>
                </div>
            </div>
        </div>
        
        {% if messages %}
            <div class="alert alert-info">
                {% for message in messages %}
                    {{ message }}
                {% endfor %}
            </div>
        {% endif %}
        
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Menu Items</h2>
            <a href="{% url 'addMeuItem' restaurant.id %}" class="btn btn-add">
                <i class="fas fa-plus"></i> Add New Menu Item
            </a>
        </div>
        
        {% if menu_items %}
            <!-- Category Navigation -->
            <div class="category-nav">
                <nav class="nav nav-pills nav-fill">
                    {% regroup menu_items by category as category_list %}
                    {% for category in category_list %}
                        <a class="nav-link {% if forloop.first %}active{% endif %}" 
                           href="#category-{{ category.grouper|slugify }}"
                           data-bs-toggle="pill">
                            {{ category.grouper }}
                        </a>
                    {% endfor %}
                </nav>
            </div>
            
            <!-- Menu Items by Category -->
            <div class="tab-content">
                {% for category in category_list %}
                    <div class="tab-pane fade {% if forloop.first %}show active{% endif %}" 
                         id="category-{{ category.grouper|slugify }}">
                        <div class="menu-category">
                            <h3 class="category-title">
                                {{ category.grouper }}
                            </h3>
                            
                            {% for item in category.list %}
                                <div class="menu-item">
                                    <img src="{{ item.image_url }}" alt="{{ item.name }}" class="menu-item-image">
                                    <div class="menu-item-details">
                                        <h5 class="menu-item-title">
                                            {% if item.is_veg %}
                                                <i class="fas fa-leaf veg-icon"></i>
                                            {% else %}
                                                <i class="fas fa-drumstick-bite non-veg-icon"></i>
                                            {% endif %}
                                            {{ item.name }}
                                            <span class="availability-badge {% if item.is_available %}available{% else %}unavailable{% endif %}">
                                                {% if item.is_available %}Available{% else %}Unavailable{% endif %}
                                            </span>
                                        </h5>
                                        <p class="menu-item-description">{{ item.description }}</p>
                                        <p class="menu-item-price">₹{{ item.price }}</p>
                                    </div>
                                    <div class="menu-item-actions">
                                        <a href="{% url 'update_menu_item' item.id %}" class="btn btn-action btn-edit">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <a href="{% url 'delete_menu_item' item.id %}" class="btn btn-action btn-delete">
                                            <i class="fas fa-trash"></i> Delete
                                        </a>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="menu-category">
                <div class="empty-category">
                    <i class="fas fa-utensils fa-3x mb-3"></i>
                    <h4>No menu items yet</h4>
                    <p>Start adding delicious items to your menu!</p>
                    <a href="{% url 'addMeuItem' restaurant.id %}" class="btn btn-add mt-3">
                        <i class="fas fa-plus"></i> Add First Menu Item
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>