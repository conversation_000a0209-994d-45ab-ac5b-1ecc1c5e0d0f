<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Restaurants - Meal Buddy</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #e00a0a;
        }

        /* Remove navbar styles from here since they're in the included navbar.html */
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            color: #333;
            text-align: center;
            margin: 30px 0;
        }

        .restaurant-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .restaurant-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: transform 0.3s, box-shadow 0.3s;
            height: 100%;
        }

        .restaurant-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .restaurant-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .restaurant-info {
            padding: 15px;
        }

        .restaurant-name {
            font-size: 1.2em;
            font-weight: bold;
            margin: 0 0 10px 0;
            color: #333;
        }

        .restaurant-details {
            color: #666;
            font-size: 0.9em;
        }

        .rating {
            color: #ffd700;
            margin: 10px 0;
        }

        .view-menu-btn {
            display: inline-block;
            padding: 8px 20px;
            background-color: #ff7e5f;
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            margin-top: 10px;
            transition: background-color 0.3s;
        }

        .view-menu-btn:hover {
            background-color: #e66f52;
            color: white;
            text-decoration: none;
        }

        .no-restaurants {
            text-align: center;
            color: #666;
            padding: 50px 0;
        }

        .back-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background-color: #45a049;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    {% include 'includes/navbar.html' %}

    <!-- Main content with proper spacing -->
    <div class="container mt-4">
        <h1>Our Restaurants</h1>

        {% if restaurants %}
            <div class="restaurant-grid">
                {% for restaurant in restaurants %}
                    <div class="restaurant-card">
                        <img src="{{ restaurant.image_url }}" alt="{{ restaurant.name }}" class="restaurant-image">
                        <div class="restaurant-info">
                            <h2 class="restaurant-name">{{ restaurant.name }}</h2>
                            <div class="restaurant-details">
                                <p><i class="fas fa-utensils me-2"></i>{{ restaurant.cuisine }}</p>
                                <p><i class="fas fa-phone me-2"></i>{{ restaurant.phone }}</p>
                                <p><i class="fas fa-clock me-2"></i>{{ restaurant.opening_hours }}</p>
                                <div class="rating">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= restaurant.rating %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <a href="{% url 'view_menu' restaurant.id %}" class="view-menu-btn">
                                    <i class="fas fa-utensils me-2"></i>View Menu
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="no-restaurants">
                <i class="fas fa-utensils fa-3x mb-3 text-muted"></i>
                <h3>No restaurants available yet</h3>
                <p class="text-muted">Check back later for new restaurants</p>
            </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

