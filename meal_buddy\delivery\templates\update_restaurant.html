<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Restaurant</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4e73df;
            --secondary-color: #f8f9fc;
            --accent-color: #2e59d9;
            --text-color: #5a5c69;
        }
        
        body {
            background-color: var(--secondary-color);
            color: var(--text-color);
            font-family: 'Nunito', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .container {
            max-width: 800px;
            margin: 40px auto;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
        }
        
        h2 {
            color: var(--primary-color);
            margin-bottom: 25px;
            font-weight: 600;
            text-align: center;
        }
        
        .form-control {
            padding: 12px 15px;
            border-radius: 6px;
            border: 1px solid #d1d3e2;
            margin-bottom: 20px;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        }
        
        label {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            padding: 10px 20px;
            font-weight: 600;
            border-radius: 6px;
        }
        
        .btn-primary:hover {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
        }
        
        .btn-secondary {
            padding: 10px 20px;
            font-weight: 600;
            border-radius: 6px;
            margin-left: 10px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 25px;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .button-group {
            display: flex;
            justify-content: flex-end;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Update Restaurant Details</h2>
        
        {% if messages %}
        <div class="messages">
            {% for message in messages %}
            <div class="alert {% if message.tags %}alert-{{ message.tags }}{% endif %}">
                {{ message }}
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <form method="POST" action="{% url 'update_restaurant' restaurant.id %}">
            {% csrf_token %}
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="name">Restaurant Name</label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ restaurant.name }}" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="cuisine">Cuisine Type</label>
                        <input type="text" class="form-control" id="cuisine" name="cuisine" value="{{ restaurant.cuisine }}" required>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="image_url">Image URL</label>
                <input type="url" class="form-control" id="image_url" name="image_url" value="{{ restaurant.image_url }}" required>
                <small class="text-muted">Enter a valid URL for the restaurant's image</small>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="phone">Contact Phone</label>
                        <input type="text" class="form-control" id="phone" name="phone" value="{{ restaurant.phone }}" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="rating">Rating</label>
                        <select class="form-control" id="rating" name="rating" required>
                            <option value="1" {% if restaurant.rating == 1 %}selected{% endif %}>★</option>
                            <option value="2" {% if restaurant.rating == 2 %}selected{% endif %}>★★</option>
                            <option value="3" {% if restaurant.rating == 3 %}selected{% endif %}>★★★</option>
                            <option value="4" {% if restaurant.rating == 4 %}selected{% endif %}>★★★★</option>
                            <option value="5" {% if restaurant.rating == 5 %}selected{% endif %}>★★★★★</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="opening_hours">Opening Hours</label>
                <input type="text" class="form-control" id="opening_hours" name="opening_hours" value="{{ restaurant.opening_hours }}" required>
                <small class="text-muted">Example: "Mon-Fri: 9AM-10PM, Sat-Sun: 10AM-11PM"</small>
            </div>
            
            <div class="button-group">
                <a href="{% url 'show_restaurants' %}" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">Save Changes</button>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>