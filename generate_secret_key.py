#!/usr/bin/env python3
"""
Generate a new Django SECRET_KEY for production use
"""
import secrets
import string

def generate_secret_key(length=50):
    """Generate a random secret key suitable for Django"""
    alphabet = string.ascii_letters + string.digits + '!@#$%^&*(-_=+)'
    return ''.join(secrets.choice(alphabet) for _ in range(length))

if __name__ == "__main__":
    secret_key = generate_secret_key()
    print("Generated SECRET_KEY:")
    print(secret_key)
    print("\nCopy this key and use it as your SECRET_KEY environment variable in Render.")
