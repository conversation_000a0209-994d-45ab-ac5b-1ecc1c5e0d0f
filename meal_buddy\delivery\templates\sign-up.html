<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign-up Page</title>
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(to right, #ffecd2, #fcb69f);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
        }
        .form-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #ff7e5f;
            border: none;
            padding: 10px 20px;
            border-radius: 30px;
            color: white;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
            transition: background-color 0.3s ease;
        }
        button:hover {
            background: #eb6a4c;
        }
        .error-message {
            color: red;
            font-size: 0.8rem;
            margin-top: 0.2rem;
        }
        .messages {
            margin-bottom: 20px;
        }
        .message {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h2>Create Account</h2>

        {% if messages %}
        <div class="messages">
            {% for message in messages %}
            <div class="message {% if message.tags %}{{ message.tags }}{% endif %}">
                {{ message }}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <form  action="{% url 'signup' %}" method="POST">
            {% csrf_token %}
            <div class="form-group">
            <label for="username">Username</label>
                <input type="text" id="username" name="username" required>
                {% if form.username.errors %}
                    <div class="error-message">
                        {% for error in form.username.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
     <div class="form-group">
            <input type="hidden" name="userType" value="customer">
    </div>
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required>
                {% if form.email.errors %}
                    <div class="error-message">
                        {% for error in form.email.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            <div class="form-group">
                <label for="mobile">Mobile</label>
                <input type="text" id="mobile" name="mobile" required>
                {% if form.mobile.errors %}
                    <div class="error-message">
                        {% for error in form.mobile.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            <div class="form-group">
                <label for="password1">Password</label>
                <input type="password" id="password1" name="password1" required>
                {% if form.password1.errors %}
                    <div class="error-message">
                        {% for error in form.password1.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            <div class="form-group">
                <label for="password2">Confirm Password</label>
                <input type="password" id="password2" name="password2" required>
                {% if form.password2.errors %}
                    <div class="error-message">
                        {% for error in form.password2.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            <div class="form-group">
                <label for="address">Address</label>
                <textarea id="address" name="address" width="2500px" height="160px" required></textarea>
                {% if form.address.errors %}
                    <div class="error-message">
                        {% for error in form.address.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            <button type="submit">Sign Up</button>
        </form>
        <p style="text-align: center; margin-top: 1rem;">
            Already have an account? <a href="{% url 'open_login' %}" style="color: #ff7e5f; text-decoration: none;">Log in</a>
        </p>
    </div>
</body>
</html>
