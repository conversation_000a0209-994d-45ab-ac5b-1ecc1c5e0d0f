{% load custom_filters %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Details - Meal Buddy Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background-color: #f0f2f5;
            min-height: 100vh;
        }

        .dashboard {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 20px;
            position: fixed;
            height: 100vh;
        }

        .logo {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 40px;
            display: flex;
            align-items: center;
        }

        .logo i {
            margin-right: 10px;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-radius: 5px;
            margin-bottom: 10px;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
        }

        .menu-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .menu-item:hover, .menu-item.active {
            background: rgba(255, 255, 255, 0.1);
        }

        .menu-item.active {
            background: #3498db;
        }

        /* Main Content Styles */
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 30px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
        }

        .order-details {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 30px;
            margin-bottom: 30px;
        }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .order-id {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
        }

        .order-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        .status-pending {
            background-color: #ffefd5;
            color: #ff9f43;
        }

        .status-confirmed {
            background-color: #e3f2fd;
            color: #2196f3;
        }

        .status-preparing {
            background-color: #e8f5e9;
            color: #4caf50;
        }

        .status-out_for_delivery {
            background-color: #fff8e1;
            color: #ffc107;
        }

        .status-delivered {
            background-color: #e8f5e9;
            color: #4caf50;
        }

        .status-cancelled {
            background-color: #ffebee;
            color: #f44336;
        }

        .order-info {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }

        .info-label {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 16px;
            font-weight: 500;
            color: #2c3e50;
        }

        .order-items {
            margin-bottom: 30px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
        }

        .items-table th, 
        .items-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .items-table th {
            font-weight: 500;
            color: #6c757d;
        }

        .item-image {
            width: 50px;
            height: 50px;
            border-radius: 5px;
            object-fit: cover;
        }

        .item-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .order-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .summary-label {
            color: #6c757d;
        }

        .summary-value {
            font-weight: 500;
            color: #2c3e50;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #dee2e6;
        }

        .total-label {
            font-weight: 600;
            color: #2c3e50;
        }

        .total-value {
            font-weight: 600;
            color: #2c3e50;
            font-size: 18px;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        .action-btn {
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
        }

        .action-btn i {
            margin-right: 8px;
        }

        .update-btn {
            background: #3498db;
            color: white;
            border: none;
        }

        .update-btn:hover {
            background: #2980b9;
        }

        .cancel-btn {
            background: #e74c3c;
            color: white;
            border: none;
        }

        .cancel-btn:hover {
            background: #c0392b;
        }

        .back-btn {
            background: transparent;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .back-btn:hover {
            background: #f8f9fa;
        }

        /* Add these styles to your existing CSS */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 20px;
            border-radius: 10px;
            width: 400px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 18px;
            color: #2c3e50;
        }

        .close-modal {
            font-size: 24px;
            cursor: pointer;
            color: #7f8c8d;
        }

        .modal-body {
            margin-bottom: 20px;
        }

        .status-select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-top: 10px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .modal-btn {
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            border: none;
            font-weight: 500;
        }

        .save-btn {
            background: #3498db;
            color: white;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <i class="fas fa-utensils"></i>
                Meal Buddy
            </div>
            <a href="{% url 'admin_home' %}" class="menu-item">
                <i class="fas fa-home"></i>
                Dashboard
            </a>
            <a href="{% url 'admin_restaurants' %}" class="menu-item">
                <i class="fas fa-store"></i>
                Restaurants
            </a>
            <a href="{% url 'admin_customers' %}" class="menu-item">
                <i class="fas fa-users"></i>
                Customers
            </a>
            <a href="{% url 'admin_orders' %}" class="menu-item active">
                <i class="fas fa-shopping-cart"></i>
                Orders
            </a>
            <a href="{% url 'admin_settings' %}" class="menu-item">
                <i class="fas fa-cog"></i>
                Settings
            </a>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="page-header">
                <h1 class="page-title">Order Details</h1>
                <a href="{% url 'admin_orders' %}" class="action-btn back-btn">
                    <i class="fas fa-arrow-left"></i> Back to Orders
                </a>
            </div>

            <div class="order-details">
                <div class="order-header">
                    <div class="order-id">Order #{{ order.id }}</div>
                    <div class="order-status status-{{ order.status }}">
                        {{ order.get_status_display }}
                    </div>
                </div>

                <div class="order-info">
                    <div class="info-card">
                        <div class="info-label">Customer</div>
                        <div class="info-value">{{ order.customer.username }}</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">Restaurant</div>
                        <div class="info-value">{{ order.restaurant.name }}</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">Order Date</div>
                        <div class="info-value">{{ order.created_at|date:"M d, Y H:i" }}</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">Delivery Address</div>
                        <div class="info-value">{{ order.delivery_address }}</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">Phone</div>
                        <div class="info-value">{{ order.customer.phone }}</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">Last Updated</div>
                        <div class="info-value">{{ order.updated_at|date:"M d, Y H:i" }}</div>
                    </div>
                </div>

                <div class="order-items">
                    <h2>Order Items</h2>
                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Price</th>
                                <th>Quantity</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in order_items %}
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center;">
                                        <img src="{{ item.menu_item.image_url }}" alt="{{ item.menu_item.name }}" class="item-image">
                                        <span class="item-name" style="margin-left: 15px;">{{ item.menu_item.name }}</span>
                                    </div>
                                </td>
                                <td>₹{{ item.price }}</td>
                                <td>{{ item.quantity }}</td>
                                <td>₹{{ item.price|floatformat:2|multiply:item.quantity }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <div class="order-summary">
                    <h2 style="margin-bottom: 15px;">Order Summary</h2>
                    <div class="summary-row">
                        <div class="summary-label">Subtotal</div>
                        <div class="summary-value">₹{{ order.total_amount|floatformat:2 }}</div>
                    </div>
                    <div class="summary-row">
                        <div class="summary-label">Delivery Fee</div>
                        <div class="summary-value">₹40.00</div>
                    </div>
                    <div class="summary-row">
                        <div class="summary-label">Tax</div>
                        <div class="summary-value">₹{{ order.total_amount|floatformat:2|multiply:0.05 }}</div>
                    </div>
                    <div class="total-row">
                        <div class="total-label">Total</div>
                        <div class="total-value">₹{{ order.total_amount|floatformat:2 }}</div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="action-btn update-btn" onclick="openStatusModal()">
                        <i class="fas fa-edit"></i> Update Status
                    </button>
                    {% if order.status != 'cancelled' and order.status != 'delivered' %}
                    <button class="action-btn cancel-btn" onclick="cancelOrder()">
                        <i class="fas fa-times"></i> Cancel Order
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Status Update Modal -->
    <div id="statusModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Update Order Status</h2>
                <span class="close-modal" onclick="closeStatusModal()">&times;</span>
            </div>
            <form id="updateStatusForm" method="post" action="{% url 'update_order_status' order.id %}">
                {% csrf_token %}
                <div class="modal-body">
                    <p>Select the new status for order #{{ order.id }}:</p>
                    <select name="status" class="status-select">
                        {% for status_value, status_name in order.STATUS_CHOICES %}
                            <option value="{{ status_value }}" {% if order.status == status_value %}selected{% endif %}>
                                {{ status_name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="modal-footer">
                    <button type="button" class="modal-btn cancel-btn" onclick="closeStatusModal()">Cancel</button>
                    <button type="submit" class="modal-btn save-btn">Update Status</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Hidden form for cancel order action -->
    <form id="cancelOrderForm" method="post" action="{% url 'update_order_status' order.id %}" style="display: none;">
        {% csrf_token %}
        <input type="hidden" name="status" value="cancelled">
    </form>
</body>
</html>

<script>
    // Modal functionality
    const modal = document.getElementById('statusModal');

    function openStatusModal() {
        modal.style.display = 'block';
    }

    function closeStatusModal() {
        modal.style.display = 'none';
    }

    // Close modal when clicking outside
    window.onclick = function(event) {
        if (event.target == modal) {
            closeStatusModal();
        }
    }
    
    // Cancel order functionality
    function cancelOrder() {
        if (confirm('Are you sure you want to cancel this order?')) {
            document.getElementById('cancelOrderForm').submit();
        }
    }
</script>


