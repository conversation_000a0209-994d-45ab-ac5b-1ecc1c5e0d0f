{% load custom_filters %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - Meal Buddy</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 800;
            color: #ff7e5f;
            font-size: 1.5rem;
        }
        
        .checkout-container {
            max-width: 900px;
            margin: 40px auto;
        }
        
        .btn-back {
            display: inline-flex;
            align-items: center;
            color: #666;
            text-decoration: none;
            margin-bottom: 20px;
            transition: color 0.3s;
        }
        
        .btn-back:hover {
            color: #333;
        }
        
        .checkout-header {
            margin-bottom: 30px;
        }
        
        .checkout-header h1 {
            color: #333;
            font-weight: 700;
        }
        
        .checkout-section {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
            padding: 25px;
            margin-bottom: 20px;
        }
        
        .section-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: 10px;
            color: #ff7e5f;
        }
        
        .restaurant-info {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .restaurant-logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
        }
        
        .restaurant-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .restaurant-cuisine {
            color: #666;
            font-size: 0.9rem;
        }
        
        .order-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .order-item:last-child {
            border-bottom: none;
        }
        
        .item-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
            margin-right: 15px;
        }
        
        .item-details {
            flex-grow: 1;
        }
        
        .item-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .item-price {
            color: #666;
            font-size: 0.9rem;
        }
        
        .item-quantity {
            background-color: #ff7e5f;
            color: rgb(230, 235, 230);
            border-radius: 50%;
            width: 50px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 0.9rem;
            margin-right: 15px;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .item-total {
            font-weight: 600;
            min-width: 80px;
            text-align: right;
        }
        
        .form-label {
            font-weight: 600;
            color: #555;
        }
        
        .payment-option {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .payment-option:hover {
            border-color: #ff7e5f;
            background-color: #fff9f8;
        }
        
        .payment-option.selected {
            border-color: #ff7e5f;
            background-color: #fff9f8;
        }
        
        .payment-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            color: #555;
        }
        
        .payment-details {
            flex-grow: 1;
        }
        
        .payment-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .payment-description {
            color: #666;
            font-size: 0.9rem;
        }
        
        .order-summary {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .summary-total {
            font-size: 1.2rem;
            font-weight: 700;
            color: #ff7e5f;
        }
        
        .btn-place-order {
            background-color: #ff7e5f;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 50px;
            font-weight: 600;
            width: 100%;
            margin-top: 20px;
            transition: background-color 0.3s;
        }
        
        .btn-place-order:hover {
            background-color: #e66f52;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    {% include 'includes/navbar.html' %}

    <div class="container checkout-container">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show mt-3" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
        
        <a href="{% url 'view_cart' %}" class="btn-back">
            <i class="fas fa-arrow-left"></i> Back to Cart
        </a>
        
        <div class="checkout-header">
            <h1>Checkout</h1>
            {% if restaurant %}
                <p>Ordering from: <strong>{{ restaurant.name }}</strong></p>
            {% endif %}
        </div>
        
        <div class="row">
            <div class="col-md-8">
                <div class="checkout-section">
                    <h2 class="section-title">
                        <i class="fas fa-map-marker-alt"></i> Delivery Address
                    </h2>
                    <form action="{% url 'process_payment' %}" method="POST">
                        {% csrf_token %}
                        <div class="form-group">
                            <label for="delivery_address" class="form-label">Delivery Address</label>
                            <textarea id="delivery_address" name="delivery_address" class="form-control" rows="3" required>{{ customer_address }}</textarea>
                        </div>
                </div>
                
                <div class="checkout-section">
                    <h2 class="section-title">
                        <i class="fas fa-utensils"></i> Order Items
                    </h2>
                    {% for item in items %}
                        <div class="order-item">
                            <img src="{{ item.image_url }}" alt="{{ item.name }}" class="item-image">
                            <div class="item-details">
                                <div class="item-name">{{ item.name }}</div>
                                <div class="item-price">₹{{ item.price }} each</div>
                            </div>
                            <div class="item-quantity">
                                Qty: {{ item.quantity }}
                            </div>
                            <div class="item-total">
                                ₹{{ item.total }}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="checkout-section">
                    <h2 class="section-title">
                        <i class="fas fa-file-invoice-dollar"></i> Order Summary
                    </h2>
                    <div class="order-summary">
                        <div class="summary-row">
                            <span>Items Total:</span>
                            <span>₹{{ total }}</span>
                        </div>
                        <div class="summary-row">
                            <span>Delivery Fee:</span>
                            <span>₹{{ delivery_fee }}</span>
                        </div>
                        <div class="summary-row">
                            <span>Tax:</span>
                            <span>₹{{ tax }}</span>
                        </div>
                        <hr>
                        <div class="summary-row summary-total">
                            <span>Total:</span>
                            <span>₹{{ final_total }}</span>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-lg btn-block">Proceed to Payment</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>








