<div class="sidebar">
    <div class="logo">
        <i class="fas fa-utensils"></i>
        <PERSON><PERSON> Buddy
    </div>
    <a href="{% url 'admin_home' %}" class="menu-item {% if request.resolver_match.url_name == 'admin_home' %}active{% endif %}">
        <i class="fas fa-home"></i>
        Dashboard
    </a>
    <a href="{% url 'admin_restaurants' %}" class="menu-item {% if request.resolver_match.url_name == 'admin_restaurants' %}active{% endif %}">
        <i class="fas fa-store"></i>
        Restaurants
    </a>
    <a href="{% url 'admin_customers' %}" class="menu-item {% if request.resolver_match.url_name == 'admin_customers' %}active{% endif %}">
        <i class="fas fa-users"></i>
        Customers
    </a>
    <a href="{% url 'admin_orders' %}" class="menu-item {% if request.resolver_match.url_name == 'admin_orders' %}active{% endif %}">
        <i class="fas fa-shopping-cart"></i>
        Orders
    </a>
    <a href="{% url 'admin_settings' %}" class="menu-item {% if request.resolver_match.url_name == 'admin_settings' %}active{% endif %}">
        <i class="fas fa-cog"></i>
        Settings
    </a>
    <a href="{% url 'logout' %}" class="menu-item logout">
        <i class="fas fa-sign-out-alt"></i>
        Logout
    </a>
</div>

<style>
    .sidebar {
        width: 250px;
        height: 100vh;
        background-color: #2c3e50;
        color: white;
        padding: 20px 0;
        position: fixed;
        left: 0;
        top: 0;
        overflow-y: auto;
    }
    
    .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: 700;
        padding: 20px;
        margin-bottom: 20px;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }
    
    .logo i {
        margin-right: 10px;
        color:  #0787dc;;
    }
    
    .menu-item {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: all 0.3s;
        border-left: 3px solid transparent;
    }
    
    .menu-item i {
        margin-right: 15px;
        width: 20px;
        text-align: center;
    }
    
    .menu-item:hover, .menu-item.active {
        background-color: rgba(255,255,255,0.1);
        color: white;
        border-left-color: #0cf87e;
    }
    
    .menu-item.logout {
        margin-top: 30px;
        border-top: 1px solid rgba(255,255,255,0.1);
        color: #f0e50b;
    }
    
    .menu-item.logout:hover {
        background-color: rgba(255,107,107,0.1);
        border-left-color: #ff6b6b;
    }
</style>